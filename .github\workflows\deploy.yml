name: CD Deploy

on:
  push:
    tags:
      - 'v*.*.*'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Docker login
        run: echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USER }} --password-stdin
      - name: Build & push image (optional)
        run: |
          docker compose -f template/docker-compose.yaml build
          docker compose -f template/docker-compose.yaml push
      - name: SSH deploy
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.PROD_SSH_KEY }}
          script: |
            cd /opt/n8n-evolution-api
            git pull
            docker compose -f template/docker-compose.yaml pull
            docker compose -f template/docker-compose.yaml up -d --wait 