name: log-sanitizer

on:
  push:
    branches: [ main ]
  pull_request:

jobs:
  detect-secrets:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install detect-secrets
        run: pip install detect-secrets
      - name: Scan logs directory
        run: |
          detect-secrets scan template/logs | tee ds_report.json
          if grep -q 'has_secrets' ds_report.json; then
            echo "⚠️ Detect-secrets encontrou potenciais segredos nos logs";
            exit 1;
          fi 