version: '3.8'
services:
  n8n_editor:
    image: n8nio/n8n:latest
    container_name: n8n_editor-1
    ports:
      - "5678:5678"
    networks:
      - app_network
    environment:
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n_fila
      - DB_POSTGRESDB_USER=postgres
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD}
      - WEBHOOK_URL=http://${HOST_IP}:5678/
    volumes:
      - ./n8n_data:/home/<USER>/.n8n
    restart: always
    depends_on:
      - postgres
networks:
  app_network:
    external: true