# =====================================================================================
# GITIGNORE - N8N EVOLUTION STACK
# Consulte PROJECT_GUIDELINES.md na raiz para regras e convenções.
# Proteção de segredos e arquivos sensíveis
# =====================================================================================

# Arquivos de ambiente e segredos
# Arquivo de ambiente local
.env
.env.*
!.env.example
template/secrets/
secrets/
*.key
*.pem
*.p12
*.pfx

# Dados de volumes Docker
template/evolution_postgres_data/
template/evolution_redis_data/
template/evolution_store/
template/grafana_data/
template/loki_data/
template/prometheus_data/
template/rabbitmq_data/
template/chatwoot_data/

# ANCHOR: Zero-Touch volumes e arquivos sensíveis
# Novos volumes Zero-Touch
postgres_data/
redis_data/
minio_data/
evolution_data/
n8n_data/
chatwoot_data/
_legacy_archive/

# Logs e arquivos temporários
logs/
*.log
*.tmp
*.temp
temp/
tmp/

# Certificados SSL/TLS
template/letsencrypt/
template/local_certs/
*.crt
*.cert
*.ca-bundle

# Backups
backups/
*.backup
*.bak
*.sql.gz
*.dump

# Arquivos de configuração local
docker-compose.override.yml
.docker/
.vscode/settings.json

# Arquivos do sistema
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# Arquivos de IDE
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Arquivos de monitoramento
monitor_logs/
checkpoints/

# Arquivos de autenticação
*.htpasswd
auth/

# Arquivos de cache
.cache/
cache/
node_modules/
.npm/

# Arquivos de build
dist/
build/
out/

# Arquivos de debug
debug.log
error.log
access.log

# Arquivos específicos do Windows
desktop.ini
$RECYCLE.BIN/

# Arquivos específicos do Linux/Mac
.Trash-*
.nfs*

# Arquivos de configuração de desenvolvimento
.devcontainer/
.gitpod.yml

# Arquivos de teste
test-results/
coverage/
.nyc_output/

# Arquivos de dependências
vendor/
packages/
