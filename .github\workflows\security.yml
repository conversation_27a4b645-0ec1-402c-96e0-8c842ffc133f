name: Security Scan

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  trivy-scan:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout c<PERSON><PERSON>
        uses: actions/checkout@v3

      - name: <PERSON>an repositório com Trivy
        uses: aquasecurity/trivy-action@v0.13.1
        with:
          scan-type: fs
          scanners: vuln,secret
          ignore-unfixed: true
          exit-code: 1
          severity: CRITICAL,HIGH 