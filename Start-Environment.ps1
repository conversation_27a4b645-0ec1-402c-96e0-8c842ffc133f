# Start-Environment.ps1 - Orquestrador Zero-Touch Robusto

# Definir o caminho do log
$LogFile = "install.log"
"" | Out-File -FilePath $LogFile -Encoding utf8

function Log-Event {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logLine = "[$timestamp][$Level] $Message"
    Add-Content -Path $LogFile -Value $logLine
    Write-Host $logLine
}

function Write-Host-Color {
    param(
        [string]$Message,
        [string]$Color
    )
    Write-Host $Message -ForegroundColor $Color
}

function Generate-RandomPassword {
    param([int]$length = 16)
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
    -join ($chars.ToCharArray() | Get-Random -Count $length)
}

function Abort-Install {
    param([string]$Message)
    Log-Event "[FATAL] $Message" "FATAL"
    Write-Host-Color "[FATAL] $Message" "Red"
    exit 1
}

function Run-Command {
    param(
        [string]$Command,
        [string]$ErrorMessage
    )
    Log-Event "Executando: $Command"
    $output = Invoke-Expression $Command 2>&1
    $output | ForEach-Object { Log-Event $_ }
    if ($LASTEXITCODE -ne 0) {
        Log-Event "$ErrorMessage`nSaída: $output" "ERROR"
        Abort-Install "$ErrorMessage (veja $LogFile para detalhes)"
    }
}

function Wait-For-ContainerHealth {
    param(
        [string]$ContainerName,
        [int]$TimeoutSeconds = 120
    )
    Write-Host-Color "Aguardando o container '$ContainerName' ficar saudável..." "Cyan"
    $startTime = Get-Date
    while ((Get-Date) -lt $startTime.AddSeconds($TimeoutSeconds)) {
        $healthStatus = docker inspect --format='{{.State.Health.Status}}' $ContainerName 2>$null
        if ($healthStatus -eq 'healthy') {
            Write-Host-Color "Container '$ContainerName' está saudável." "Green"
            return
        }
        if ($healthStatus -eq 'unhealthy') {
            Abort-Install "Container '$ContainerName' reportou um status 'unhealthy'. Verifique os logs com 'docker logs $ContainerName'."
        }
        # Se o status for 'starting' ou vazio, continua esperando.
        Start-Sleep -Seconds 5
    }
    Abort-Install "Tempo de espera esgotado para o container '$ContainerName' ficar saudável."
}

# 1. Validação de pré-requisitos
Write-Host-Color "Validando pré-requisitos..." "Cyan"
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) { Abort-Install "Docker não está instalado ou não está no PATH." }
if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue) -and -not (docker compose version 2>$null)) {
    Abort-Install "Docker Compose não está instalado ou não está no PATH."
}
if ($PSVersionTable.PSVersion.Major -lt 5) { Abort-Install "PowerShell 5.1 ou superior é necessário." }

# 2. Validação de arquivos essenciais
$requiredFiles = @('postgres.yml','redis.yml','minio.yml','evolution.yml','chatwoot.yml','n8n.yml')
foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) { Abort-Install "Arquivo obrigatório '$file' não encontrado na raiz. Corrija e execute novamente." }
}

# 3. Gestão de Configuração (.env)
$envFile = ".env"
if (-not (Test-Path $envFile)) {
    Write-Host-Color "Arquivo .env não encontrado. Gerando novas credenciais..." "Yellow"
    
    # Lógica de detecção de IP aprimorada
    $netProfiles = Get-NetConnectionProfile | Where-Object { $_.IPv4Connectivity -eq 'Internet' }
    if ($netProfiles.Count -eq 0) { $netProfiles = Get-NetConnectionProfile } # Fallback se nenhum tiver "Internet"
    if ($netProfiles.Count -gt 1) {
        Write-Host-Color "Múltiplas interfaces de rede ativas encontradas. Usando a primeira: $($netProfiles[0].InterfaceAlias)" "Yellow"
    }
    if ($netProfiles.Count -eq 0) {
        Abort-Install "Nenhuma interface de rede ativa encontrada para determinar o IP do host."
    }
    $hostIp = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias $netProfiles[0].InterfaceAlias).IPAddress
    if (-not $hostIp) { Abort-Install "Não foi possível determinar o IP do Host a partir da interface $($netProfiles[0].InterfaceAlias)." }

    $postgresPassword = Generate-RandomPassword
    $minioPassword = Generate-RandomPassword
    $evolutionApiKey = Generate-RandomPassword
    @"
POSTGRES_PASSWORD=$postgresPassword
MINIO_ROOT_USER=admin
MINIO_ROOT_PASSWORD=$minioPassword
AUTHENTICATION_API_KEY=$evolutionApiKey
HOST_IP=$($hostIp[0])
"@ | Out-File -FilePath $envFile -Encoding utf8
    Write-Host-Color "Arquivo .env criado com sucesso!" "Green"
} else {
    Write-Host-Color "Usando arquivo .env existente." "Green"
}

# Carrega as variáveis do .env para a sessão atual
Get-Content $envFile | ForEach-Object {
    $line = $_.Trim()
    if ($line -and $line -notlike '#*' -and $line -match "=") {
        $key, $value = $line.Split('=', 2)
        [System.Environment]::SetEnvironmentVariable($key.Trim(), $value.Trim())
    }
}

# 4. Criação da Rede Docker
Write-Host-Color "Verificando a rede Docker 'app_network'..." "Cyan"
$networkExists = docker network ls | Select-String -Pattern "app_network"
if (-not $networkExists) {
    Run-Command "docker network create app_network" "Falha ao criar a rede Docker 'app_network'."
    Write-Host-Color "Rede 'app_network' criada." "Green"
} else {
    Write-Host-Color "Rede 'app_network' já existe." "Green"
}

# 5. Limpeza e Inicialização dos Serviços
$composeFiles = "-f postgres.yml -f redis.yml -f minio.yml -f evolution.yml -f chatwoot.yml -f n8n.yml"

Write-Host-Color "Parando e removendo containers antigos para garantir um início limpo..." "Cyan"
# Usamos 'down' para remover containers de execuções anteriores. Erros são ignorados se nada existir.
docker compose $composeFiles down --remove-orphans 2>$null | Out-Null

Write-Host-Color "Iniciando todos os serviços (PostgreSQL, Redis, MinIO, Evolution, Chatwoot, n8n)..." "Cyan"
# Adicionado --force-recreate para garantir que os containers sejam recriados, evitando conflitos de execuções anteriores.
Run-Command "docker compose $composeFiles up -d --build --force-recreate" "Falha ao iniciar os serviços com Docker Compose"

# 6. Validar container postgres_aula
# Espera o PostgreSQL ficar saudável antes de continuar
Wait-For-ContainerHealth "postgres_aula"

# 7. Setup do Banco de Dados
function Create-Db-If-Not-Exists {
    param([string]$DbName)
    # Comando psql idempotente para criar o banco de dados somente se ele não existir.
    # Usamos \gexec para executar dinamicamente o comando CREATE DATABASE.
    # As aspas (`") são escapadas para serem passadas literalmente pelo PowerShell para o shell do container.
    $command = "docker exec postgres_aula psql -U postgres -c `"SELECT 'CREATE DATABASE $DbName' WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$DbName')\gexec`""
    Run-Command $command "Falha ao criar banco de dados '$DbName'"
}
  
 Write-Host-Color "Criando extensão pgvector e bancos de dados..." "Cyan"
 Run-Command "docker exec postgres_aula psql -U postgres -c `"CREATE EXTENSION IF NOT EXISTS vector;`"" "Falha ao criar extensão vector"
 Create-Db-If-Not-Exists "chatwoot"
 Create-Db-If-Not-Exists "evolution"
 Create-Db-If-Not-Exists "n8n_fila"

# 8. Validar container chatwoot-rails-1
Wait-For-ContainerHealth "chatwoot-rails-1"

# 9. Migração do Chatwoot
Run-Command "docker exec chatwoot-rails-1 rails db:chatwoot_prepare" "Falha na migração do Chatwoot"
Write-Host-Color "Migração do Chatwoot concluída." "Green"

# 10. Gerar Dashboard e Exibir Resumo
if (Test-Path './Generate-ServiceDashboard.ps1') {
    Write-Host-Color "Gerando dashboard de serviços..." "Cyan"
    ./Generate-ServiceDashboard.ps1
} else {
    Write-Host-Color "Dashboard não gerado: script ausente." "Yellow"
}

Write-Host-Color "--------------------------------------------------" "Magenta"
Write-Host-Color "      AMBIENTE DE AUTOMAÇÃO INICIADO COM SUCESSO!      " "Magenta"
Write-Host-Color "--------------------------------------------------" "Magenta"
Write-Host-Color "Acesse os serviços:" "White"
Write-Host-Color "  - n8n: http://localhost:5678" "White"
Write-Host-Color "  - Evolution API: http://localhost:8080" "White"
Write-Host-Color "  - Chatwoot: http://localhost:3000" "White"
Write-Host-Color "  - MinIO Console: http://localhost:9001" "White"

Write-Host-Color "`nCredenciais Geradas (salve em local seguro):" "Yellow"
Get-Content $envFile