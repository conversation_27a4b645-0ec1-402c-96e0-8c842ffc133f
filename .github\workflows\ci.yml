name: CI

on:
  push:
  pull_request:

jobs:
  tests:
    runs-on: windows-latest

    steps:
      - name: Checkout có<PERSON>
        uses: actions/checkout@v3

      - name: Instalar Pester
        shell: pwsh
        run: |
          Install-Module -Name Pester -Force -SkipPublisherCheck -Scope CurrentUser

      - name: Executar Testes de Diagnóstico Mkcert (Unit)
        shell: pwsh
        run: |
          Set-StrictMode -Version Latest
          Write-Host "Iniciando testes Unit"
          exit (pwsh -File "template/tests/Test-DiagnosticoMkcert.ps1" -TestScope Unit)

      - name: Detectar vazamento de segredos (trufflehog)
        uses: trufflesecurity/trufflehog@v3.53
        with:
          scanArguments: --fail --no-update --exclude-path .git

  lint-docker:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Hadolint Dockerfiles
        uses: hadolint/hadolint-action@v3
        with:
          dockerfile: '**/Dockerfile*'

  compose-validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Validate docker compose syntax
        run: docker compose -f template/docker-compose.enterprise.yaml config -q

  smoke-test:
    runs-on: ubuntu-latest
    services:
      docker:
        image: docker:24.0.5-dind
        privileged: true
        options: >-
          --registry-mirror=https://registry-1.docker.io
    steps:
      - uses: actions/checkout@v4
      - name: Start stack (10-min timeout)
        run: |
          docker compose -f template/docker-compose.enterprise.yaml pull
          docker compose -f template/docker-compose.enterprise.yaml up --wait --timeout 600
      - name: Check health status
        run: docker compose ps
      - name: Tear down
        if: always()
        run: docker compose -f template/docker-compose.enterprise.yaml down -v 