#version: '3.7'
services:
  evolution_v2:
    image: atendai/evolution-api:latest
    container_name: evolution_aula
    networks:
      - app_network
    ports:
      - "8080:8080"
    volumes:
      - ./evolution_data:/evolution/instances
    environment:
      - DATABASE_CONNECTION_URI=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/evolution
      - S3_ACCESS_KEY=${MINIO_ROOT_USER}
      - S3_SECRET_KEY=${MINIO_ROOT_PASSWORD}
      - S3_BUCKET=evolution
      - S3_ENDPOINT=http://minio:9000
      - S3_PORT=9000
      - S3_USE_SSL=false
      - S3_FORCE_PATH_STYLE=true
      - AUTHENTICATION_API_KEY=${AUTHENTICATION_API_KEY}
    restart: always
    depends_on:
      - postgres
      - minio
networks:
  app_network:
    external: true