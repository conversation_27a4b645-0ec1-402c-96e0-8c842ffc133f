name: CI Health

on:
  push:
    branches: [ main ]
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Compose up
        run: docker compose -f template/docker-compose.yaml up -d --wait
      - name: Health checks
        run: |
          curl -f http://localhost:9090/-/ready
          curl -f http://localhost:9093/-/ready
          curl -f http://localhost:3100/ready || true
          curl -k -f https://n8n.localhost/healthz || true
      - name: Compose down
        if: always()
        run: docker compose -f template/docker-compose.yaml down -v 