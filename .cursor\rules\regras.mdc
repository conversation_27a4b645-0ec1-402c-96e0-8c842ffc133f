---
description: 
globs: 
alwaysApply: true
---
Read these guidelines.

Make sure the documentation file is up to date.

Include a reference to this file in prompts (e.g., "See PROJECT_GUIDELINES.md in the project root for rules and conventions"), or review all files and create initial information.

Use anchor comments in your code to provide additional context.

Guidelines Maintenance

Review and update this document regularly as your project evolves.

Reflect changes in collaboration practices or project conventions here.


Periodically review anchor comments in your code and remove them only with human approval if necessary.