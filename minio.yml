#version: '3.8'
services:
  minio:
    image: minio/minio:latest
    container_name: minio_aula
    command: server /data --console-address ":9001"
    networks:
      - app_network
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    volumes:
      - ./minio_data:/data
    restart: always
networks:
  app_network:
    external: true