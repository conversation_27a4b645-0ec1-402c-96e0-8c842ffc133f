﻿<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Dashboard de ServiÃ§os - N8N Evolution</title>
  <style>
    body{font-family:Arial,sans-serif;background:#f4f6f8;margin:0;padding:0}
    header{background:#0066cc;color:#fff;padding:20px;text-align:center}
    main{padding:20px;max-width:1200px;margin:0 auto}
    .controls{margin-bottom:20px}
    .controls button{padding:8px 16px;background:#0066cc;color:#fff;border:none;border-radius:4px;cursor:pointer}
    table{width:100%;border-collapse:collapse;background:#fff;border-radius:8px;overflow:hidden}
    th,td{padding:12px 16px;border-bottom:1px solid #eee;text-align:left}
    th{background:#f0f4f8;font-weight:600}
    tbody tr:hover{background:#fafafa}
    .status-dot{width:12px;height:12px;border-radius:50%;display:inline-block;margin-right:8px;background:#ccc}
    .status-dot.online{background:#28a745}
    .status-dot.offline{background:#dc3545}
    .status-dot.checking{background:#ffc107}
    .status-text{font-weight:500}
    .cred-btn{background:#17a2b8;color:#fff;border:none;padding:4px 8px;border-radius:3px;cursor:pointer;font-size:.75rem}
    .cred-cell{font-family:monospace;background:#f8f9fa;padding:4px 8px;border-radius:3px;font-size:.8rem}
  </style>
</head>
<body>
  <header>
    <h1>Dashboard de ServiÃ§os - N8N Evolution</h1>
  </header>
  <main>
    <div class="controls">
      <button onclick="checkStatus()" id="checkBtn">Verificar Status</button>
    </div>
    <table>
      <thead><tr><th>Status</th><th>ServiÃ§o</th><th>EndereÃ§o</th><th>DescriÃ§Ã£o</th><th>Container</th><th>Credenciais</th></tr></thead>
      <tbody>        <tr data-service='n8n'>
          <td><span class='status-dot'></span><span class='status-text'>Desconhecido</span></td>
          <td><strong>N8N</strong></td>
          <td><a href='https://n8n.localhost:443' target='_blank'>https://n8n.localhost:443</a></td>
          <td>Editor de workflows</td>
          <td><code>n8n</code></td>
          <td><button class='cred-btn' onclick='this.nextElementSibling.hidden=false;this.hidden=true'>Mostrar</button><span hidden class='cred-cell'>1FNyOqaM8DHE:DH1jkt0IElvX2efiCR9wFf2poQCTlOrv</span></td>
        </tr>
        <tr data-service='chatwoot_app'>
          <td><span class='status-dot'></span><span class='status-text'>Desconhecido</span></td>
          <td><strong>Chatwoot</strong></td>
          <td><a href='https://chatwoot.localhost:443' target='_blank'>https://chatwoot.localhost:443</a></td>
          <td>Atendimento omnichannel</td>
          <td><code>chatwoot_app</code></td>
          <td><button class='cred-btn' onclick='this.nextElementSibling.hidden=false;this.hidden=true'>Mostrar</button><span hidden class='cred-cell'>admin@localhost:qospjLZvtksB5fHERmCM1SJfzyk5QIQ0</span></td>
        </tr>
        <tr data-service='grafana'>
          <td><span class='status-dot'></span><span class='status-text'>Desconhecido</span></td>
          <td><strong>Grafana</strong></td>
          <td><a href='https://grafana.localhost:443' target='_blank'>https://grafana.localhost:443</a></td>
          <td>Dashboards e mÃ©tricas</td>
          <td><code>grafana</code></td>
          <td><button class='cred-btn' onclick='this.nextElementSibling.hidden=false;this.hidden=true'>Mostrar</button><span hidden class='cred-cell'>admin:pPO4xhAZop6ameyCebEOpW0Pay7XnpyF</span></td>
        </tr>
        <tr data-service='prometheus'>
          <td><span class='status-dot'></span><span class='status-text'>Desconhecido</span></td>
          <td><strong>Prometheus</strong></td>
          <td><a href='http://prometheus.localhost:9090' target='_blank'>http://prometheus.localhost:9090</a></td>
          <td>Coleta de mÃ©tricas</td>
          <td><code>prometheus</code></td>
          <td>N/A</td>
        </tr>
        <tr data-service='metabase'>
          <td><span class='status-dot'></span><span class='status-text'>Desconhecido</span></td>
          <td><strong>Metabase</strong></td>
          <td><a href='https://metabase.localhost:443' target='_blank'>https://metabase.localhost:443</a></td>
          <td>BI simplificado</td>
          <td><code>metabase</code></td>
          <td>N/A</td>
        </tr>
        <tr data-service='rabbitmq'>
          <td><span class='status-dot'></span><span class='status-text'>Desconhecido</span></td>
          <td><strong>RabbitMQ</strong></td>
          <td><a href='http://localhost:15672' target='_blank'>http://localhost:15672</a></td>
          <td>Fila de mensagens</td>
          <td><code>rabbitmq</code></td>
          <td><button class='cred-btn' onclick='this.nextElementSibling.hidden=false;this.hidden=true'>Mostrar</button><span hidden class='cred-cell'>rabbit_user:lRkNH335A9unQvDaPxRepZjoF10TMIqX</span></td>
        </tr>
        <tr data-service='loki'>
          <td><span class='status-dot'></span><span class='status-text'>Desconhecido</span></td>
          <td><strong>Loki</strong></td>
          <td><a href='http://localhost:3100' target='_blank'>http://localhost:3100</a></td>
          <td>Logs agregados</td>
          <td><code>loki</code></td>
          <td>N/A</td>
        </tr>      </tbody>
    </table>
  </main>
  <script>
    async function checkStatus() {
      const dots = document.querySelectorAll('.status-dot');
      const statusTexts = document.querySelectorAll('.status-text');
      
      document.getElementById('checkBtn').textContent = 'Verificando...';
      
      for (let i = 0; i < dots.length; i++) {
        const dot = dots[i];
        const statusText = statusTexts[i];
        
        dot.className = 'status-dot checking';
        if (statusText) statusText.textContent = 'Verificando...';
        
        try {
          const response = await fetch(window.location.origin, {
            method: 'HEAD',
            mode: 'no-cors',
            cache: 'no-cache'
          });
          
          dot.className = 'status-dot online';
          if (statusText) statusText.textContent = 'Online';
          
        } catch (e) {
          dot.className = 'status-dot offline';
          if (statusText) statusText.textContent = 'Offline';
        }
      }
      
      document.getElementById('checkBtn').textContent = 'Verificar Status';
    }
    
    document.addEventListener('DOMContentLoaded', checkStatus);
  </script>
</body>
</html>