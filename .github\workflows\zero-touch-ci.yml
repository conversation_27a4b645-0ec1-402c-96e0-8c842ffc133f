name: Zero-Touch CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

permissions:
  contents: read

jobs:
  lint-and-security:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Hadolin<PERSON> Dockerfiles
        uses: hadolint/hadolint-action@v3.1.0
        with:
          dockerfile: |
            **/Dockerfile*
      - name: PSScriptAnalyzer
        uses: microsoft/psscriptanalyzer-action@v1.0.2
        with:
          path: ./
      - name: Trivy FS scan
        uses: aquasecurity/trivy-action@0.15.0
        with:
          scan-type: fs
          ignore-unfixed: true
          severity: HIGH,CRITICAL

  compose-up-test:
    needs: lint-and-security
    runs-on: ubuntu-latest
    services:
      docker:
        image: docker:24.0.7-dind
        options: >-
          --privileged --registry-mirror https://mirror.gcr.io
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install PowerShell & Docker CLI
        run: |
          apk add --no-cache curl bash git docker-cli
          wget -q https://github.com/PowerShell/PowerShell/releases/download/v7.4.0/powershell-7.4.0-linux-alpine-x64.tar.gz -O /tmp/ps.tar.gz
          mkdir /pwsh && tar -xzf /tmp/ps.tar.gz -C /pwsh
          ln -s /pwsh/pwsh /usr/bin/pwsh
      - name: Pull base images
        run: docker compose -f template/docker-compose.yaml pull --quiet
      - name: Start stack & wait (900 s)
        run: |
          pwsh -File ./Start-Environment.ps1 -ForceCleanup -Verbose
      - name: Check container health
        run: |
          unhealthy=$(docker ps --filter "health=unhealthy" --format '{{.Names}}')
          if [ -n "$unhealthy" ]; then
            echo "Unhealthy containers: $unhealthy"; exit 1; fi
      - name: Stop stack
        if: always()
        run: docker compose -f template/docker-compose.yaml down -v --remove-orphans 