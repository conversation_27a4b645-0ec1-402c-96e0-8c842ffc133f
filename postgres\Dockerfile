# Usar uma imagem base específica do PostgreSQL para garantir a reprodutibilidade.
FROM postgres:15-bookworm

# Instalar as dependências necessárias para compilar a extensão.
# Usar --no-install-recommends para manter a imagem menor.
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    git \
    postgresql-server-dev-15 \
    && rm -rf /var/lib/apt/lists/*

# Clonar, compilar e instalar uma versão específica do pgvector.
# Fixar a versão (ex: v0.5.1) é crucial para builds consistentes.
RUN git clone --branch v0.5.1 https://github.com/pgvector/pgvector.git /usr/src/pgvector \
    && cd /usr/src/pgvector \
    && make \
    && make install

# Limpar as dependências de compilação para reduzir o tamanho final da imagem.
RUN apt-get purge -y --auto-remove build-essential git postgresql-server-dev-15